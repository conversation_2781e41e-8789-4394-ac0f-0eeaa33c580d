use tauri::Manager;
use tauri_plugin_opener::OpenerExt;

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn showWindow(window: tauri::Window) {
    // show current window
    window.show();
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            let window = tauri::WebviewWindowBuilder::from_config(app.handle(), &app.config().app.windows[0])?.build()?;
            window.hide();
            // window.open_devtools();

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            showWindow,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
